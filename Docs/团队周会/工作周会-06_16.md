# 工作周会 - 06/16

## 上周工作内容

1. IB V2.0.1
   开发人员：@胡海彬 @胡胜武 @苏波

- 线上回归测试问题处理
- 应 Boss 要求，对行情的 UI 做了一版优化，将在下个常规版本一起发布
- 本周六已对外开放
- 已在 dpm 上发布

2. Followme Android 包报病毒问题继续整改
   开发人员：@芦磊
   处理 Followme 报毒问题，具体情况请查阅 Followme 报毒问题整改

- 基本完成整改
- 收据收集风险问题
- Firebase 日志记录 - 需调研
- DataDog 数据加密处理

3. IM 迁移到 STD
   开发人员：@芦磊、@刘艺峰

- iOS 端基本完成代码迁移，需要等后端接口发布才能开始联调
- Android 端本周开始

4. Followme v6.3.0
   开发人员：@舒柳洋 @刘江 @刘艺峰 @穆桂海 @胡林春

- 计划需求完成时间点：8 月初
- 目前两端都已全力投入开发中

5. 常规版本

- KBI 应用市场物料更改，已上线一个常规版本 @苏波 @胡海彬
- Followme 6.6.5 常规版本官网包发布上线，以及 Google 包提审以及发布上线，官网包本周未出现报病毒问题 @卢磊 @胡林春
- 全埋点方案已完成调研，尽量去解决自动埋点的页面名称两端不一致的问题，已输出完整的文档和两端的 Demo: https://gitweb.io/bos-std/app/framework/AutoTrackManager。@苏波
- AI 开发提效的调研和尝试 @苏波
  - 目前先在 iOS 端调研，已完成基本的 AI 行为规范以及操作文档
  - 支持自动生成项目架构、技术架构、任务管理、任务追踪等多种文档
  - 尝试 Feedback 页面，两次对话完成了整个 UI，对比设计稿的完成度还是比较高
  - 还需要完善的地方： - 生成的代码需要适合我们的编码规范和习惯 - 需适配内部的框架，如网络框架 - 主题和字体还无法做到精细化适配，这个还得继续去优化 AI 的规范文档

## 本周计划：

- BOS IM 迁移和接口对接
- Followme v6.3.0 需求开发
- 常规需求开发：活动接口对接、IB2.1 调整
- BOS 新版 UI 同步到其他平台
- Android 视频压缩方案 - 6 月底 @芦磊 @胡海彬
