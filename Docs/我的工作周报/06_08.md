## 本周工作内容

### Followme 6.2.0 收尾

开发人员：@胡林春 @穆桂海
解决 Google 包审核被拒问题，重新提审审核通过
官网包删除静态分析的一些无用链接，发了一个常规包，暂未报病毒，完整的解决方案还需要下一个报
Followme Android 包报病毒问题
开发人员：@芦磊
处理 Followme 报毒问题，具体情况请查阅 Followme 报毒问题整改
a.gray.sexBLTWbindsnake 处理方案：在腾讯手机管家多次测试后目前暂无报毒情况 （暂没发布）
移除 com.android.installreferrer:installreferrer:2.2 库引用
移除包含且不限于以下权限:
android.permission.CALL_PHONE
android.permission.ACCESS_MEDIA_LOCATION
android.permission.GET_ACCOUNTS
android.permission.GET_TASKS
android.permission.PACKAGE_USAGE_STATS
android.permission.RECEIVE_BOOT_COMPLETED
android.permission.READ_CONTACTS
com.google.android.gms.permission.AD_ID
android.permission.WRITE_CONTACTS
android.permission.ACCESS_COARSE_LOCATION
android.permission.ACCESS_FINE_LOCATION
android.permission.ACCESS_BACKGROUND_LOCATION
android.permission.FOREGROUND_SERVICE
android.permission.REQUEST_INSTALL_PACKAGES
android.permission.INTERACT_ACROSS_USERS_FULL
permission.RECEIVE_MSG
Trojan-Spy.AndroidOS.Agent: 这个可能表示一种具有通用特性的 Android 恶意软件，它可能不直接针对短信功能，而是可能有更广泛的恶意行为，比如数据窃取、远程控制、权限滥用等。它可能通过其他方式来达到目的，例如后台运行、隐藏自身、监控用户活动等。该病毒未在 apk 安装时显示报毒，优先级可降低处理
潜在风险点：
依然存在三方类库中的敏感权限
后台监控用户数据/行为并记录上报，datadog 和 firebase 等
应用检测更新下载并自动安装

### IB V2.0.1

开发人员：@胡海彬 @胡胜武
完成阿拉伯语验收
完成回归测试

### IM 迁移到 STD

开发人员：@芦磊、@刘艺峰
iOS 端基本完成代码迁移，需要等后端接口发布才能开始联调
Android 端要下周才能开始

### Followme v6.3.0

开发人员：@舒柳洋 @穆桂海 @刘江 @胡林春 @苏波
完成需求任务拆分以及开发排期，大概 40 人/工作日，需求完成时间点：8 月初
部分人员已开始投入开发，下周所有人员都将全力投入开发

### 常规版本

KBP 常规版本发布上线
DPM 常规版本发布上线
神策全埋点方案调研，完成 70%

## 下周工作计划

Followme v6.3.0 投入开发
Followme Android 包申诉
IB V2.0.1 线上验证以及发布
KBP 支持 Hindi、Urdu 以及阿拉伯语
DPM 活动需求开发
神策全埋点方案完成调研
