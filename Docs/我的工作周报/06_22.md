## 本周工作内容

### Followme 6.3.0 项目

计划需求完成时间点：8 月初
本周开发内容：
iOS：@舒柳洋 @刘江
可展开/收起列表封装
自适应布局数据展示控件封装
Copying 列表、Copied 列表编写
完成 Feed 流通用模块的 UI 优化，新增图片预览器支持视频和无限滑动加载

Android：@穆桂海 @胡林春
完成头部视图与交互页面
编辑页面、昵称、简介页面开发
背景图、头像设置页面实现
分段控制器功能完成
Portfolio 部分 UI 开始

### BOS v2.1.0 for im upgrade

开发人员：@芦磊、@刘艺峰
iOS 端完成代码迁移，完成收发消息等接口联调
移除旧的野火 SDK
Android 端初步完成代码迁移工作
常规版本 BOS v1.17.21
开发人员：@胡胜武、@胡海彬
增加 visa 和电子钱包入
同步行情 UI
目前已在 DPI、DPM、KBP、KBI 均已发布上线

### 其他日常工作

Welcome rewards DPM 活动接口对接完
配合运营人员调试深链问题，目前邮件里面的链接老是会多封装一层 URL，导致深链的逻辑失效，需要看下邮件模板是否存在问题
线上闪退问题的分析和解决
舒柳洋 技术分享：《SwiftUI 编程思想》

## 下周工作计划

Followme 6.3.0 项目开发和跟进 - @舒柳洋 @刘江 @穆桂海 @胡林春
Android 端视频压缩方案调研 - @胡海彬
BOS v2.1.0 for im upgrad 联调与自测 @芦磊、@刘艺峰
BOS 常规需求 @胡胜武、@胡海彬
年中绩效考核梳理
