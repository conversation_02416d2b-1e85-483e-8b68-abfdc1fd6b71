## 本周工作内容

### IB V2.0.1

开发人员：@胡海彬 @胡胜武 @苏波
线上回归测试问题处理
应 Boss 要求，对行情的 UI 做了一版优化，将在下个常规版本一起发布
本周六已对外开放

### Followme Android 包报病毒问题继续整改

开发人员：@芦磊
处理 Followme 报毒问题，具体情况请查阅 Followme 报毒问题整改
清理项目中冗余/无用的代码
清理 fmlib 仓库下未使用的 lib
移除首页常驻监听截屏操作-敏感操作
进一步清理权限： - android.permission.FOREGROUND_SERVICE - android.permission.READ_PHONE_STATE - android.permission.MANAGE_EXTERNAL_STORAGE
暂时移除 fmlogger 和 datadog 埋点和日志上报，后续可使用 firebase 统一处理
处理现状：目前已无 a.gray. sexBLTWbindsnake 报毒，在部分平台仍有 Trojan-Spy.AndroidOS.Agent，该病毒是过多搜集用户数据并明文上报引发，不影响用户正常安装使用
尝试加固和重新签名，可改善一些敏感/可疑操作分

### IM 迁移到 STD

开发人员：@芦磊、@刘艺峰
iOS 端基本完成代码迁移，需要等后端接口发布才能开始联调
Android 端要下周才能开始

### Followme v6.3.0

计划需求完成时间点：8 月初
目前两端都已全力投入开发中
本周开发内容：
iOS：@舒柳洋 @刘江 @刘艺峰
个人主页基础框架搭建，支持多级嵌套
Portfolio 主界面 UI 编写
Overview 界面、Copying 界面 UI 编写
账户管理，页面搭建，账号列表弹出
Individual Page 个人信息编辑 UI
我的页面基本 Ul
首页的基础结构重构
iOS 端由于有 刘艺峰 加入协助开发，目前进度还是比较快的。
Android：@穆桂海 @胡林春
个人首页基础架构搭建，完成多层嵌套布局和吸顶效果
处理多层级滑动的联动和细节优化
完成我的页面 UI
Home 页面 UI 调整完成 40%

### 常规版本

KBI 应用市场物料更改，已上线一个常规版本 @苏波 @胡海彬
Followme 6.6.5 常规版本官网包发布上线，以及 Google 包提审以及发布上线，官网包本周未出现报病毒问题 @卢磊 @胡林春
全埋点方案已完成调研，尽量去解决自动埋点的页面名称两端不一致的问题，已输出完整的文档和两端的 Demo: https://gitweb.io/bos-std/app/framework/AutoTrackManager。@苏波

#### AI 开发提效的调研和尝试 @苏波

目前先在 iOS 端调研，已完成基本的 AI 行为规范以及操作文档
支持自动生成项目架构、技术架构、任务管理、任务追踪等多种文档
尝试 Feedback 页面，两次对话完成了整个 UI，对比设计稿的完成度还是比较高
还需要完善的地方：
生成的代码需要适合我们的编码规范和习惯
主题和字体还无法做到精细化适配，这个还得继续去优化 AI 的规范文档

## 下周工作计划

BOS IM 迁移和接口对接
Followme v6.3.0 需求开发
常规需求开发，活动接口对接
继续 AI 开发提效的调研
