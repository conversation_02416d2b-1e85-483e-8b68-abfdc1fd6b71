## 本周工作内容

### 1. Followme 6.3.0 项目

本周开发内容：

- iOS: @舒柳洋 @刘江
  - Copier 筛选、Copying 列表、通用筛选排序控件、弹窗封装
  - NetworkKit 支持不同模块不同域名配置
  - Overview、Performance 接口协议对接
  - 持仓、历史订单列表
  - About 基本信息页面、post-info、filter、以及对接长短微博，发送相册图片
  - FM6.6.5 版本的 bug 修复以及提审上线
- Android: @穆桂海 @胡林春
  - 从信任的交易商开户
  - 账户展示卡片（可滑动、可交互）
  - 设置隐私或公开，账户连接/记录、disconnect/delete account
  - 隐藏 Positions
  - 完成 userabout 列表展示、添加删除等功能
  - 完成 Home 页面资讯、工具等页面点击事件处理和展示
  - 完成 Home 页面推荐人列表点击展示功能 30%

### 2. BOS v2.0.7 for im upgrade

- 开发人员: @卢磊、@刘艺峰
  - imsdk 正式迁移完毕代码，新版本需求评审
  - 版本排期并进入开发，移除原生野火依赖和初始化流程
  - 升级并优化 datadog 依赖使用，修复部分 bug
  - 增加个人信息中 uid 和在线时间等字段
  - 需求评审、细化，确定开发范围
  - IMKit 聊天页面、私信详情页面、最近会话列表处理
  - BOS 代码分支调整，改为基于 STD

### 3. 常规需求和技术调研

- 开发人员: @胡胜武、@胡海彬
  - FMMutipleScrollViewHelper 快速点击切换 crash 问题尝试修复
  - DPM 活动需求提测，问题跟进
  - BOS 活动需求对接，目前已在 dpm 项目提测，beta 环境测试结束
  - Android DPI KBI 拍摄视频压缩技术调研，目前选中技术 ffmpeg 压缩。已在 demo
    完成测试，后续将集成到项目中提测

### 4. 其他日常工作

Followme Android 端又重新报病毒问题了，已整理了一个变动较大的版本，目前已提交给测试验收
使用 Gemini CLI 作为我们的 Code Review 工具调研

- 在本地调试通过
- 结合 Gitlab CI/CD ，每当有 Merge Requests 时，就会自动触发 Gemini CLI 的 Code Review 工作流
- 完成之后，会在下面生成一条评论，分了 3 个级别的标志：🔴 严重问题 🟡 需要改进 🟢 良好实践
- 初步体验的效果还是不错的，结合代码规范文档，还是能个指出不少问题。就是需要一台 设备 作为 gitlab-runner，而且这个 gitlab-runner 要 时刻连上 Zero Trust ，所以需要一台专门作为 CI/CD 的设备，目前是用我自己的电脑作为 gitlab-runner，用起来非常不方便：1. 存储空间的问题 2. 网络的问题，经常要切换网络

## 下周工作计划

- Followme 6.3.0 项目开发和跟进 - @舒柳洋 @刘江 @穆桂海 @胡林春
- BOS v2.0.7 for im upgrade 需求继续完善 - @卢磊、@刘艺峰
- DPM 活动需求上线 - @胡胜武、@胡海彬
- Android 视频压缩方案完成调研
