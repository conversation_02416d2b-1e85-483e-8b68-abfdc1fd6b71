## 本周工作内容

### Followme v6.2.0

参与人员：@芦磊 @胡林春 @刘艺峰 @刘江 @苏波 @穆桂海
合入版本 v6.1.1 内容：
App 两端翻译统一
支持 Light 和 Dark 两套主题
阿拉伯语支持（Beta）
新增需求：
部分自动刷新逻辑
iOS 通用链接问题修复
Google 包的图片权限适配 - 第二轮测试前加上
当前项目进度：
完成所有需求开发
开始自测，处理自测 bug
预计下周提测

### IB V2.0

参与人员：@胡海彬 @胡胜武
协助开发：@穆桂海 @舒柳洋
完成重新排期和需求分工
@胡海彬 @胡胜武 负责 IB 相关的需求开发
@穆桂海 @舒柳洋 负责行情、账户等剩余需求的开发
预计两周内完成开发

### 88 常规 - 同步登录优化版本内容

参与人员：@穆桂海 @舒柳洋
本周完成测试，两端都已提审并审核通过。

### KBI 跟进

@苏波
已确认需要重新整改，等产品和设计给过来整改规划
代码混淆工作，已完成的部分

1.  项目名和二进制名称修改
2.  依赖库名称更改，以及大部分静态库依赖改成动态库，这样可以大大减小主程序文件大小 3. 资源文件梳理，以及图片资源压缩
    代码混淆，还可以做的操作：
    自动加入一些垃圾库和垃圾代码，最好的办法是通过自动化脚本，在打包之前加入垃圾代码，完成打包后将这些更改重置，这样也不会影响到原有的代码，以及带来的代码和合并问题，这个需要研究和实现。

### 其他工作

翻译同步工具问题修复 - @舒柳洋
梳理 iFollowme 完全隔离环境 - @苏波

## 下周工作计划

Followme v6.2.0 项目提测
IB v2.0 项目需求开发
KBI 整改
翻译同步工具完成交付
iFollowme 申请开发者账号以及整改规划
